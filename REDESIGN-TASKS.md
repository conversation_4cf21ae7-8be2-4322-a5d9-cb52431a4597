### Phase 1: Core Component & Theming Refinements ✅ COMPLETED

*   **Task 1.1: Refine `RecipeCard.tsx` UI** ✅
    *   In `src/components/RecipeCard.tsx`:
    *   ✅ Wrap the `Card` component with a `Box` and add `data-testid={'recipe-card-' + recipe.id}` to the new `Box`. This will help with testing.
    *   ✅ Add a `FavoriteIcon` / `FavoriteBorderIcon` `IconButton` to the top-right corner of the `CardMedia` area, absolutely positioned.
    *   ✅ Make this new favorite `IconButton` clickable to toggle the recipe's `isFavorite` status, calling `onUpdate` prop.
    *   ✅ Remove the "Add to Favorites" / "Remove from Favorites" `MenuItem` from the three-dot menu.
    *   ✅ In the `CardContent` section, replace the text labels for `prepTime` and `servings` with MUI Icons.
        *   ✅ Create a `Box` with `display: 'flex', alignItems: 'center', mb: 1`.
        *   ✅ Inside, add `<AccessTimeIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />`.
        *   ✅ Follow it with the `Typography` for the time.
        *   ✅ Do the same for servings using `<RestaurantIcon />`.
    *   ✅ Modify the tag display logic to show a maximum of 2 tags with `Chip` components. If there are more than 2, show a third `Chip` with the label `+${recipe.tags.length - 2}`.

*   **Task 1.2: Refine Global Theme in `theme.ts`** ✅
    *   In `src/theme.ts`:
    *   ✅ In the `components.MuiCard.styleOverrides`, add a subtle `transition: 'box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out'` and a `transform: 'translateY(-4px)'` to the `&:hover` pseudo-class.
    *   ✅ Increase the `shape.borderRadius` to `10` for a softer, more modern look on all components that use it.

### Phase 2: Hub-Specific UI Refinements ✅ COMPLETED

*   **Task 2.1: Enhance Dashboard UI (`Dashboard.tsx`)** ✅
    *   In `src/pages/Dashboard.tsx`:
    *   ✅ Refactor the main layout to use an MUI `Grid` container with a two-column layout on `md` screens and up.
    *   ✅ Create a new "What's for Dinner?" widget as the first item. It should fetch today's meal plan and display the recipe assigned to 'dinner'. If no dinner is planned, it should show a `Button` to "Find a Recipe" that navigates to `/cookbook?q=dinner`.
    *   ✅ Convert the "Quick Actions" `Box` into its own `Card` widget. Replace the `Button` components with larger, more prominent `Paper` or `Card` elements containing an `Icon` and `Typography`.

*   **Task 2.2: Enhance Cookbook Filters (`Cookbook.tsx`)** ✅
    *   In `src/pages/Cookbook.tsx`, within the "All Recipes" `TabPanel`:
    *   ✅ Above the results `Grid`, add a `Box` for "Quick Filters".
    *   ✅ Inside this `Box`, render a group of `Chip` components for the most common difficulties ('Easy', 'Medium', 'Hard'). These should be clickable to toggle the `filters.difficulty` state.
    *   ✅ Update the `fetchRecipes` logic to incorporate the new quick filters alongside the existing advanced filters.

*   **Task 2.3: Enhance Pantry UI (`PantryManager.tsx`)** ✅
    *   In `src/components/PantryManager.tsx`:
    *   ✅ Inside the `ListItemText` `secondary` prop for each pantry item, replace the text `"Ingredient: No ingredient mapped"` with an `IconButton` containing a `LinkIcon`.
    *   ✅ The `onClick` handler for this new `IconButton` should open the `IngredientAssociationModal` for that item.
    *   ✅ If an ingredient *is* mapped, display the linked ingredient name as before.

### Phase 3: Workflow and Interaction Enhancements

*   **Task 3.1: Refactor Cooking Mode Layout (`CookingMode.tsx`)**
    *   In `src/pages/CookingMode.tsx`:
    *   Modify the main layout `Box`. For non-mobile views (`!isMobile`), use `display: 'flex'` to create a two-panel layout.
    *   The first panel (`flex: '0 0 300px'`) should contain the "Ingredients" list.
    *   The second panel (`flex: 1`) should contain the current instruction `Paper`.
    *   Increase the `fontSize` of the instruction `Typography` component to `1.5rem` on desktop and `1.3rem` on mobile.
    *   For mobile views (`isMobile`), implement the drawer logic:
        *   Add a `Drawer` component from MUI, anchored to the bottom. Its visibility should be controlled by a new `ingredientsDrawerOpen` state.
        *   Add a `Fab` component with a `RestaurantIcon` that sets `ingredientsDrawerOpen` to `true`.
        *   The "Ingredients" list, currently shown in the side panel on desktop, should be rendered inside this `Drawer` on mobile.

*   **Task 3.2: Implement Shopping List Grouping (`ShoppingListView.tsx`)**
    *   In `src/components/ShoppingListView.tsx`:
    *   Add a `calculateShoppingListProgress` helper function in `shoppingListStorage.ts` that returns `{ completed, total, percentage }`.
    *   At the top of the `ShoppingListView`, display a `LinearProgress` bar showing the completion percentage.
    *   Refactor the item rendering logic. Instead of a flat `List`, use the `groupShoppingListItemsByCategory` helper from `shoppingListStorage.ts`.
    *   Iterate through the grouped categories. For each category, render a `Paper` component with a `Typography` header (e.g., "Produce").
    *   Inside each category `Paper`, render the `List` of `ListItem`s for the items in that category.
    *   When an item is checked, apply `textDecoration: 'line-through'` and `opacity: 0.6` to its `ListItem` style.

### Phase 4: Final Polish

*   **Task 4.1: Implement Search History in `SearchBar.tsx`**
    *   In `src/components/SearchBar.tsx`:
    *   Use `useState` to manage `searchHistory` and a `popover`'s `anchorEl`.
    *   When the `TextField` is focused (`onFocus`), if there is search history, set the `anchorEl` to open an MUI `Popover`.
    *   Inside the `Popover`, render a `List` of recent searches from the `searchHistory` state.
    *   Clicking a history item should set the `searchTerm`, trigger `onSearch`, and close the popover.
    *   When `handleSearch` is called, update the `searchHistory` state with the new term, keeping it unique and limited to 5 items.
